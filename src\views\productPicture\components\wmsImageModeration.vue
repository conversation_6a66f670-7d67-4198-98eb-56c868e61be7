<template>
    <div class="preview-original">
        <!-- 预览原图对话框 -->
        <el-dialog :title="title" :visible.sync="show"
            :width="rowData[skuIndex].pictureVersionType === 5 ? '1200px' : '1000px'" height="80vh" class="dlg1"
            @close="close">
            <div class="preview-box">
                <draggable class="drag-wrap" v-model="draggableData" style="background: #ffffff">
                    <el-row :gutter="10"><el-col :span="row.title == '器械许可证图片列表' ? 24 : 8"
                            v-for="(row, parIndex) in draggableData" :key="parIndex">
                            <el-card>
                                <div class="title">{{ row.title }}</div>
                                <draggable class="img-wrap" :list="row.imgList" :group="{ name: 'row' }">
                                    <el-col :span="row.title == '器械许可证图片列表' ? 8 : 24"
                                        v-for="(item, index) in row.imgList" :key="index">
                                        <el-card>
                                            <span v-if="item.pictureStatus !== 1 && item.deleteStatus === 0 && showBtn"
                                                @click="deleteImg(item, 'delete')" class="delete-btn">删除</span>
                                            <span v-if="item.pictureStatus !== 1 && item.deleteStatus === 1 && showBtn"
                                                class="delete-btn cancel" @click="deleteImg(item, 'cancel')">取消删除</span>
                                            <img @click="previewImg(index, parIndex)" :class="{
                                                abnormal: item.styleState == 1,
                                                normal: item.styleState == 2,
                                            }" :src="item.pictureUrl" alt="" />
                                            <span v-if="rowData[skuIndex].applyCode.indexOf('STP') == -1">{{
                                                item.pictureName }}</span>
                                        </el-card>
                                    </el-col>
                                </draggable>
                            </el-card>
                        </el-col>
                    </el-row>
                </draggable>
                <div class="update-bind-box" v-if="rowData[skuIndex].pictureVersionType === 5">
                    <p>换绑机构：</p>
                    <p v-for="(item, index) in allData.bindMechanismList" :key="index">{{ item }}</p>
                    <br>
                    <p>原绑定版本号：</p>
                    <img :src="allData.sourcePictureVersionMainPictureUrl" alt="">
                    <p>{{ allData.sourcePictureVersion }}</p>
                </div>
            </div>
            <div class="btn-wrap" v-if="showBtn">
                <el-button-group style="">
                    <el-button v-if="showBtn" type="primary" @click="pass">审核通过</el-button>
                    <el-button v-if="showBtn" type="primary" @click="refuse">运营驳回</el-button>
                </el-button-group>
            </div>
        </el-dialog>
        <preview-img ref="previewImg" :productInfo="rowData[skuIndex]" :webList="allData"></preview-img>
    </div>
</template>

<script>
import {
    getOriginalTaskReceivedDetail,
    auditTaskReceived
} from "@/api/productPicture.js";
import draggable from "vuedraggable";
import previewImg from "@/components/uploadImg/previewImg";
export default {
    name: "",
    components: {
        draggable,
        previewImg
    },
    filters: {},
    props: {},
    watch: {},
    data() {
        return {
            showBtn: true,
            rowData: {},
            draggableData: [],
            preImgList: [],
            preIndex: 0,
            title: "",
            allData: [],
            skuIndex: 0,
            show: false,
            auditOption: "",
        };
    },
    computed: {},
    watch: {},
    created() { },
    mounted() { },
    methods: {
        // 预览
        previewImg(index, parIndex) {
            let flag = true;
            this.preIndex = 0;
            this.preImgList = [];
            this.draggableData.forEach((list, dParIndex) => {
                list.imgList.forEach((item, dIndex) => {
                    if (dParIndex == parIndex && dIndex == index) {
                        flag = false;
                    }
                    if (flag) {
                        this.preIndex++;
                    }
                    this.preImgList.push(item);
                });
            });
            this.$refs.previewImg.openDlg(
                this.preImgList,
                this.preIndex,
                this.rowData[this.skuIndex].smallPackageCode
            );
        },
        // 关闭的回调
        closeDlg() {
            this.show = false;
        },
        // 打开的回调
        async openDlg(allData, skuIndex) {
            this.rowData = allData
            this.skuIndex = skuIndex
            this.showBtn = allData[skuIndex].auditStatus ? false : true
            const res = await getOriginalTaskReceivedDetail(allData[skuIndex].id)
            console.log(res)
            if (!res.retCode) {
                this.allData = res.data
                this.getDragDate()
                this.getDiaTitle()
                this.show = true
            } else {
                this.$message.error(res.retMsg)
            }
        },
        // 弹框标题
        getDiaTitle() {
            const data = this.allData
            if (data.createUserMechanismName) {
                this.title = `${data.pictureVersion}_${data.createUser}_${data.createUserMechanismName}`;
            } else {
                this.title = `${data.pictureVersion}_${data.createUser}`;
            }
        },
        // 图片列表数据格式处理
        getDragDate() {
            if (this.rowData[this.skuIndex].applyCode.indexOf("STP") != -1) {
                this.draggableData = [{ imgList: [], title: "器械许可证图片列表" }];
                this.allData.productPictureList.forEach(
                    (item) => {
                        this.draggableData[0].imgList.push(item);
                    }
                );
            } else {
                this.draggableData = [
                    { imgList: [], title: "主图" },
                    { imgList: [], title: "外包装" },
                    { imgList: [], title: "说明书" },
                ];
                this.allData.productPictureList.forEach(
                    (item) => {
                        let img = new Image();
                        img.src = item.pictureUrl;
                        // 别人写的看不懂的样式代码
                        img.onload = () => {
                            if (item.readonly || item.auditStatus == 1) {
                                if (item.pictureOrdinal == 1) {
                                    if (img.height < 800 || img.height < 800) {
                                        item.styleState = 1;
                                    } else {
                                        item.styleState = 2;
                                    }
                                    this.$set(this.draggableData, 0, this.draggableData[0]);
                                } else if (
                                    item.pictureOrdinal >= 2 &&
                                    item.pictureOrdinal <= 5
                                ) {
                                    if (img.width < 800) {
                                        item.styleState = 1;
                                    } else {
                                        item.styleState = 2;
                                    }
                                    this.$set(this.draggableData, 1, this.draggableData[1]);
                                } else {
                                    if (img.width < 800) {
                                        item.styleState = 1;
                                    } else {
                                        item.styleState = 2;
                                    }
                                    this.$set(this.draggableData, 2, this.draggableData[2]);
                                }
                            }
                        };
                        if (item.pictureOrdinal == 1) {
                            this.draggableData[0].imgList.push(item);
                        } else if (item.pictureOrdinal >= 2 && item.pictureOrdinal <= 5) {
                            this.draggableData[1].imgList.push(item);
                        } else {
                            this.draggableData[2].imgList.push(item);
                        }
                    }
                );
            }
        },
        // 删除原图
        deleteImg(item, type) {
            if (type == "delete") {
                item.deleteStatus = 1;
            } else {
                item.deleteStatus = 0;
            }
        },
        // 运营驳回
        refuse() {
            this.$prompt("审核不通过原因", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                inputPattern: /\S/,
                inputErrorMessage: "审核不通过原因必填",
            })
                .then(({ value }) => {
                    this.auditOption = value;
                    this.submitDealData(2);
                })
                .catch(() => {
                    this.auditOption = ''
                    this.$message({
                        type: "info",
                        message: "取消输入",
                    });
                });
        },
        // 审核通过
        pass() {
            this.submitDealData(1);
        },
        submitDealData(auditStatus) {
            let arr = [];
            let flag = true;
            this.draggableData.forEach((list, parentIndex) => {
                let normalArr = list.imgList.filter((item) => {
                    return item.deleteStatus == 0;
                });
                if (this.rowData[this.skuIndex].applyCode.indexOf("STP") == -1) {
                    if (parentIndex == 0 && normalArr.length != 1) {
                        this.$message.error("主图数量必须为1张");
                        flag = false;
                        return;
                    }
                    if (parentIndex == 1 && normalArr.length > 4) {
                        this.$message.error("外包装最多只能4张");
                        flag = false;
                        return;
                    }
                    if (parentIndex == 2 && normalArr.length > 5) {
                        this.$message.error("说明书最多只能5张");
                        flag = false;
                        return;
                    }
                } else {
                    if (parentIndex == 0 && normalArr.length > 10) {
                        this.$message.error("器械许可证图片数量最多只能10张");
                        flag = false;
                        return;
                    }
                }
                if (flag) {
                    let index = 0;
                    list.imgList.forEach((item) => {
                        if (this.rowData[this.skuIndex].applyCode.indexOf("STP") == -1) {
                            if (parentIndex == 0) {
                                item.pictureOrdinal = 1;
                            } else if (parentIndex == 1) {
                                if (item.deleteStatus == 1) {
                                    item.pictureOrdinal = 2;
                                } else {
                                    item.pictureOrdinal = index + 2;
                                    if (index != 3) {
                                        index++;
                                    }
                                }
                            } else if (parentIndex == 2) {
                                if (item.deleteStatus == 1) {
                                    item.pictureOrdinal = 6;
                                } else {
                                    item.pictureOrdinal = index + 6;
                                    if (index != 4) {
                                        index++;
                                    }
                                }
                            }
                        } else {
                            if (item.deleteStatus == 1) {
                                item.pictureOrdinal = 11;
                            } else {
                                item.pictureOrdinal = index + 11;
                                index++;
                            }
                        }

                        let obj = {
                            pictureId: item.pictureId,
                            pictureOrdinal: item.pictureOrdinal,
                            deleteStatus: item.deleteStatus
                        };
                        arr.push(obj);
                    });
                }
            });
            if (flag) {
                let state = auditStatus
                this.postReviewPictures(arr, state);
            }
        },
        postReviewPictures(productPictureList, auditStatus) {
            auditTaskReceived({
                id: this.rowData[this.skuIndex].id,
                auditStatus,
                auditOption: this.auditOption,
                productPictureList,
            }).then((res) => {
                if (res.retCode == 0) {
                    this.$message.success("操作成功！");
                    this.$emit("refresh");
                    this.closeDlg();
                } else {
                    this.$message.error(res.retMsg);
                }
            });
        }
    },
};
</script>

<style lang="scss" scoped>
.drag-wrap /deep/ {
    padding: 0 10px;
    width: 100%;

    .el-card__body {
        padding: 10px;
    }

    .title {
        text-align: center;
        font-size: 20px;
        font-weight: bold;
        padding-bottom: 10px;
    }
}

.img-wrap /deep/ {
    .el-card__body {
        display: flex;
        align-items: center;
        justify-content: center;
        // padding-left: 50px;
        // flex-direction: de;
        position: relative;

        .watermark {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 9;
            color: #fff;
            font-size: 12px;
            text-align: center;

            &.delete {
                background: #f56c6c;
            }

            &.reject {
                background: #ffcc33;
            }

            &.pass {
                background: #108f40;
            }
        }

        .delete-btn {
            position: absolute;
            top: 0;
            right: 0;
            font-size: 12px;
            color: #fff;
            padding: 5px;
            background: #f56c6c;
            border-top-right-radius: 5px;

            &.cancel {
                background: #909399;
            }
        }

        span {
            flex: 1;
            text-align: center;
            position: relative;
            padding-left: 5px;
        }
    }

    img {
        height: 80px;
        width: 80px;
        border: 5px solid #fff;

        &.abnormal {
            border-color: #f56c6c;
        }

        &.normal {
            border-color: #67c23a;
        }
    }
}

.preview-box {
    display: flex;
    align-items: center;
}

.circle {
    z-index: 2000;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    opacity: 0.5;
    background-color: #ccc;
    text-align: center;
    font-size: 22px;
    line-height: 30px;
    transition: opacity 0.3s;
}

.circle-lf {
    left: 20px;
}

.circle-rt {
    right: 20px;
}

.circle:hover {
    opacity: 1;
}

.btn-wrap {
    display: flex;
    justify-content: flex-end;
    padding: 20px 10px 10px 0;
}

.update-bind-box {
    margin-left: 40px;

    img {
        width: 150px;
        height: 150px;
    }
}
</style>